<script setup lang="ts">
import { useRouteQuery } from '@vueuse/router';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { TabPane, Tabs } from '@/components';
import { DEPT_TYPE } from '@/constant';
import DepartmentPerformance from './components/DepartmentPerformance.vue';
import DepartmentTree from './components/DepartmentTree.vue';
import PersonnelSituation from './components/PersonnelSituation.vue';
import ProductionAnalysis from './components/ProductionAnalysis.vue';

defineOptions({
  name: 'Department',
});

const router = useRouter();
const deptType = useRouteQuery<string>('type');
const deptName = useRouteQuery<string>('deptName');
const activeTab = ref('personnelSituation');
const isSalesOrMarket = computed(() => {
  return [DEPT_TYPE.SALES as string, DEPT_TYPE.MARKET as string].includes(deptType.value);
});

function goBack() {
  if (window.history.length > 1) {
    router.back();
  }
  else {
    router.push('/overview');
  }
}

watch(isSalesOrMarket, (newValue) => {
  if (newValue) {
    activeTab.value = 'departmentPerformance';
  }
  else {
    activeTab.value = 'personnelSituation';
  }
}, { immediate: true });
</script>

<template>
  <div class="grid h-full grid-cols-3 rounded-md bg-[#F7F8FA] px-24 py-14">
    <div class="flex flex-col">
      <div
        class="grid h-32 w-76 cursor-pointer place-content-center rounded-sm border border-[#DCDFE6] bg-white text-sm text-[#606266]"
        @click="goBack"
      >
        返回
      </div>
      <!--      <div class="mb-24 mt-49 text-lg font-semibold text-[#3A4762]"> -->
      <!--        当前范围内组织架构 -->
      <!--      </div> -->
      <div class="-ml-24 flex-1">
        <DepartmentTree />
      </div>
    </div>
    <div
      class="col-span-2 h-full overflow-y-hidden rounded-md rounded-tl-none bg-white p-24 shadow"
    >
      <div class="flex items-center justify-between">
        <div class="text-2xl font-bold">
          {{ deptName }}
        </div>
      </div>
      <Tabs v-model="activeTab" type="card" class="mt-16 overflow-y-scroll">
        <TabPane v-if="isSalesOrMarket" label="部门绩效" name="departmentPerformance">
          <DepartmentPerformance />
        </TabPane>
        <TabPane v-if="isSalesOrMarket" label="投产分析" name="productionAnalysis">
          <ProductionAnalysis />
        </TabPane>
        <TabPane label="人员情况" name="personnelSituation">
          <PersonnelSituation />
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>

<style lang="css" scoped></style>
