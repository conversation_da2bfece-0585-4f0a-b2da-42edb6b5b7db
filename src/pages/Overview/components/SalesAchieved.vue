<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query';
import { useRouteQuery } from '@vueuse/router';
import { ElProgress } from 'element-plus';
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { fetchGeneralSellerStatus } from '@/api/general';
import { Comparison, SourceCard } from '@/components';
import { formatDecimal } from '@/lib';
import BusinessCard from './BusinessCard.vue';

const router = useRouter();
const statStartTime = useRouteQuery('statStartTime', '', { transform: Number });
const statEndTime = useRouteQuery('statEndTime', '', { transform: Number });
const province = useRouteQuery('province', undefined, { transform: val => val || undefined });

const { data: salesData } = useQuery({
  queryKey: ['销售达成', statStartTime, statEndTime, province],
  queryFn: () =>
    fetchGeneralSellerStatus({
      statStartTime: statStartTime.value,
      statEndTime: statEndTime.value,
      provinceName: province.value,
    }),
});

const indicators = computed(() => {
  if (!salesData.value) {
    return [];
  }

  const investAmount = salesData.value.reimburseAmount + salesData.value.salaryAmount;
  return [
    { label: '销售指标', value: salesData.value?.sellerQuotaNum },
    { label: '完成量', value: salesData.value?.quotaCompleteNum },
    {
      label: '完成率',
      value: `${salesData.value?.quotaCompleteRate ?? 0}%`,
    },
    { label: '资金投入(万)', value: formatDecimal(investAmount / 10000, 2) },
    { label: '产出金额(万)', value: formatDecimal(salesData.value?.effectiveOrderAmount / 10000, 2) },
    {
      label: '投产比',
      value: formatDecimal(
        (salesData.value?.effectiveOrderAmount || 0) / (investAmount || 1),
        2,
      ),
    },
  ];
});

const percentage = computed(() => {
  const quotaCompleteRate = salesData.value?.quotaCompleteRate || 0;
  return (quotaCompleteRate || 0) > 100 ? 100 : quotaCompleteRate;
});

/**
 * 跳转到销售页面
 */
function handleSellAnalysis() {
  router.push('/sell');
}
</script>

<template>
  <SourceCard title="销售达成" action-text="销售分析" class="flex-1" @action="handleSellAnalysis">
    <Comparison
      :yoy="salesData?.yoyQuotaPercent"
      :mom="salesData?.momQuotaPercent"
      class="gap-16 text-[12px]"
    />
    <ElProgress
      :percentage="percentage"
      :stroke-width="8"
      class="3xl:mb-12 3xl:mt-8"
    >
      <div class="text-xs text-[#3A4762]">
        {{ salesData?.sellerQuotaNum }}（{{ salesData?.quotaCompleteRate }}%）
      </div>
    </ElProgress>
    <div class="grid grid-cols-[30%_40%_30%] gap-y-10 text-nowrap text-xs text-[#7A8599]">
      <template v-for="(item, index) in indicators" :key="index">
        <div
          class="inline-flex gap-16"
          :class="[index % 3 !== 0 ? 'border-l border-[#9FBCF5] pl-35' : '']"
        >
          <span>{{ item.label }}</span>
          <span class="text-[#3A4762]">{{ item.value }}</span>
        </div>
      </template>
    </div>
    <div class="flex gap-16 overflow-x-scroll">
      <BusinessCard
        v-for="area in salesData?.deptSellers || []"
        :key="area.departmentName"
        :title="area.departmentName"
        class="shrink-0"
        :target="{
          target: area.sellerQuotaNum || 0,
          done: area.quotaCompleteNum || 0,
          name: '销售指标',
        }"
        :yoy="area.yoyQuotaPercent"
        :mom="area.momQuotaPercent"
        :style="{ width: 'calc(50% - 8px)' }"
      >
        <template #footer>
          <div
            class="mt-8 flex items-center justify-between rounded-md bg-[#e6eeff] px-10 py-8 text-xs font-normal text-[#7A8599]"
          >
            <div>
              <div>资金投入</div>
              <div class="mt-2 text-[#3A4762]">
                {{ formatDecimal(((area.salaryAmount || 0) + (area.reimburseAmount || 0)) / 10000, 2) }}万
              </div>
            </div>
            <div>/</div>
            <div>
              <div>产出金额</div>
              <div class="mt-2 text-[#3A4762]">
                {{ formatDecimal(area.effectiveOrderAmount / 10000, 2) }}万
              </div>
            </div>
            <span>=</span>
            <div>
              <div>投产比</div>
              <div class="mt-2 text-[#3A4762]">
                {{
                  formatDecimal((area?.effectiveOrderAmount || 0) / ((area.salaryAmount || 0) + (area.reimburseAmount || 0) || 1), 2)
                }}
              </div>
            </div>
          </div>
        </template>
      </BusinessCard>
    </div>
  </SourceCard>
</template>
